<?php
session_start(); // Always start the session at the very beginning

// Redirect function for clean redirects
function redirectWithStatus($page, $status, $message) {
    header('Location: ' . $page . '?status=' . urlencode($status) . '&message=' . urlencode($message));
    exit();
}

// Auto-logout after 90 minutes (5400 seconds) of inactivity
$inactivity_timeout = 5400; // 90 minutes * 60 seconds/minute

// Check current login status
$is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$is_user_logged_in = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;

// Get the logged-in user's ID
$logged_in_user_id = $_SESSION['user_id'] ?? null;
$logged_in_admin_id = $_SESSION['admin_id'] ?? null;

// --- <PERSON>le Logout request first ---
if (isset($_GET['logout']) && $_GET['logout'] == 'true') {
    $logged_out_type = $_SESSION['logged_in_type'] ?? 'user'; // Default to user logout page
    session_unset();   // Unset all session variables
    session_destroy(); // Destroy the session

    // Redirect to the appropriate login page after logout
    if ($logged_out_type === 'admin') {
        redirectWithStatus('admin.php', 'success', 'You have been logged out.');
    } else { // Assume it was a local user or general public trying to logout from this page
        redirectWithStatus('user_login.php', 'success', 'You have been logged out.');
    }
}

// --- Enforce Login for Shared Pages ---
// If NEITHER admin NOR local user is logged in, redirect to user_login.php
if (!$is_admin_logged_in && !$is_user_logged_in) {
    redirectWithStatus('user_login.php', 'error', 'Please log in to access this page.');
}

// --- Auto-logout check for ACTIVE session (either admin or user) ---
if (($is_admin_logged_in || $is_user_logged_in) && isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $inactivity_timeout)) {
    $logged_out_type = $_SESSION['logged_in_type'] ?? 'user'; // Capture type before unsetting
    session_unset();
    session_destroy();
    if ($logged_out_type === 'admin') {
        redirectWithStatus('admin.php', 'error', 'You were logged out due0 to inactivity.');
    } else {
        redirectWithStatus('user_login.php', 'error', 'You were logged out due to inactivity.');
    }
}

// Update last activity time and store user type (Crucial for auto-logout redirect)
// Only update if someone is actively logged in
if ($is_admin_logged_in) {
    $_SESSION['last_activity'] = time();
    $_SESSION['logged_in_type'] = 'admin';
} elseif ($is_user_logged_in) {
    $_SESSION['last_activity'] = time();
    $_SESSION['logged_in_type'] = 'user';
}
// --- END CORRECTED AUTHENTICATION LOGIC ---

// Generate CSRF token for form submission (after authentication check)
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MJ Hauling United LLC - Leads Formatter</title>
    <style>
        /* Base Styles */
        html, body {
            height: 100%; /* Keep height for body to allow stretching for footer if needed */
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            overflow-x: hidden; /* Prevent horizontal scrolling */
        }

        body {
            font-family: 'Poppins', sans-serif; /* Modern, readable font */
            background: linear-gradient(135deg, #f0f4f8 0%, #dbe2ed 100%); /* Soft, modern gradient */
            color: #333;
            display: flex; /* Use flexbox for the body */
            flex-direction: column; /* Stack children vertically */
            align-items: center; /* Center content horizontally */
            overflow-y: auto; /* Allow the body to scroll vertically */
            padding-top: 80px; /* **Crucial:** Push content down to clear fixed navbar */
            padding-left: 25px; /* Enhanced horizontal padding */
            padding-right: 25px; /* Enhanced horizontal padding */
            position: relative;
        }

        /* Navbar Styles */
        .navbar {
            background: #2c7be5; /* Stronger primary blue */
            padding: 12px 30px; /* Slightly more generous padding */
            border-radius: 0;
            margin-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Softer shadow */
            width: 100%;
            max-width: none;
            box-sizing: border-box;
            position: fixed; /* Keep navbar fixed */
            top: 0;
            left: 0;
            z-index: 1000;
        }
        .navbar .site-title {
            font-size: 2rem; /* Larger title */
            font-weight: 700;
            color: #ffffff;
            flex-grow: 1;
            margin-right: 25px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.15);
        }
        .menu-icon {
            display: none; /* Hidden by default on larger screens */
            color: #ffffff;
            font-size: 2.2rem; /* Larger icon */
            cursor: pointer;
            padding: 5px;
            z-index: 1001;
        }
        .navbar-links {
            display: flex;
            gap: 25px; /* More spacing for links */
        }
        .navbar-links a {
            color: #ffffff;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 25px; /* Pill-shaped buttons */
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
            font-weight: 500;
            background-color: rgba(255, 255, 255, 0.15); /* Slightly transparent background */
        }
        .navbar-links a:hover {
            background-color: rgba(255, 255, 255, 0.3); /* More opaque on hover */
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* Main Container Styles */
        .container {
            background: #ffffff;
            padding: 35px; /* Increased padding */
            border-radius: 20px; /* More rounded corners */
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15); /* Deeper shadow */
            width: 100%;
            max-width: 1280px; /* Slightly wider max-width */
            margin-top: 0; /* No margin-top here as body padding handles spacing */
            margin-bottom: 30px; /* Add bottom margin to separate from footer */
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            flex-grow: 1; /* Allow container to grow and push footer down */
        }
        h1 {
            display: none;
        }

        /* Form Elements */
        label {
            display: block;
            margin-bottom: 8px; /* More space below label */
            font-weight: 600;
            color: #4a5568; /* Softer text color */
            font-size: 1.08rem;
        }
        textarea, input[type="text"], input[type="date"], input[type="number"], input[type="email"], select {
            width: 100%;
            margin-bottom: 18px; /* Default margin, overridden in combined groups */
            padding: 14px; /* More padding */
            font-size: 1rem;
            border: 1px solid #cbd5e0; /* Softer border color */
            border-radius: 10px; /* Slightly more rounded */
            box-sizing: border-box;
            transition: border-color 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            background-color: #f7fafc; /* Lighter background */
        }
        textarea:focus, input:focus, select:focus {
            border-color: #2c7be5; /* Blue focus border */
            outline: none;
            box-shadow: 0 0 0 4px rgba(44, 123, 229, 0.2); /* Softer, broader glow */
        }
        #inputData { /* Specific style for raw data textarea */
            resize: none; /* Disable manual resizing */
            min-height: 100px; /* Smaller height */
        }
        #formattedMessage {
            background: #e6f0ff; /* Lighter blue background */
            border: 2px solid #5a9cf8; /* Solid border with blue */
            min-height: 120px; /* Smaller height */
            resize: vertical;
            flex-grow: 1;
            font-family: monospace; /* Monospaced font for code/data-like output */
            color: #1a202c; /* Darker text */
        }

        /* Form Rows and Groups (General) */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            gap: 25px; /* Increased gap for better spacing */
            margin-bottom: 25px;
            width: 100%;
        }
        .form-group {
            flex-grow: 1;
            flex-shrink: 1;
            flex-basis: 0;
            min-width: 220px; /* Slightly larger min-width */
        }
        /* Specific adjustment for the first row to keep 4 items in a row */
        .form-row.top-inputs .form-group {
            flex-basis: calc(25% - 18.75px); /* Adjusted calc for 25px gap */
            min-width: 190px;
        }
        .form-group.full-width {
            flex-basis: 100%;
        }
        .form-group label {
            margin-bottom: 5px;
        }
        .form-group input, .form-group select, .form-group textarea {
            margin-bottom: 0;
        }
        .form-group textarea {
            /* min-height is now defined directly for #inputData and #formattedMessage */
        }

        /* Flex Container for Raw Data + Buttons */
        .raw-data-section {
            display: flex;
            flex-direction: row;
            gap: 25px; /* Consistent gap */
            margin-bottom: 25px;
            align-items: flex-end; /* Align buttons to the bottom of the textarea */
            flex-wrap: wrap;
            width: 100%;
        }

        .formatted-message-section {
            display: flex;
            flex-direction: row;
            gap: 25px;
            margin-bottom: 25px;
            align-items: center; /* Changed to center to align tick message */
            flex-wrap: wrap;
            width: 100%;
        }


        .raw-data-section .form-group,
        .formatted-message-section .form-group {
            flex-grow: 1;
            flex-basis: 0;
            min-width: 300px; /* Larger min-width for textareas */
        }
        .raw-data-section .form-group textarea,
        .formatted-message-section .form-group textarea {
             margin-bottom: 0;
        }

        .raw-data-buttons, .formatted-message-buttons {
            display: flex;
            flex-direction: column; /* Stack buttons vertically */
            gap: 12px; /* Slightly larger gap between buttons in column */
            flex-shrink: 0;
            align-self: stretch; /* Make buttons stretch to fill available vertical space if textarea is taller */
            /* Ensure buttons within this group have consistent width */
            width: 180px; /* Fixed width for the button column on larger screens */
        }
        .raw-data-buttons button, .formatted-message-buttons button {
            margin-top: 0;
            height: 100%; /* Make buttons fill available height */
            white-space: nowrap;
            padding: 12px 20px; /* More generous padding for buttons */
            font-size: 1.05rem; /* Slightly larger font */
            font-weight: 600; /* Bolder text */
            transition: background-color 0.3s ease, transform 0.2s ease, box-shadow 0.2s ease;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1); /* Softer button shadow */
            border-radius: 8px; /* Slightly more rounded buttons */
            border: none; /* Ensure no default border */
            cursor: pointer;
            /* Ensure buttons take full width of their parent .raw-data-buttons / .formatted-message-buttons */
            width: 100%;
            box-sizing: border-box; /* Include padding/border in width */
        }
        .raw-data-buttons button:hover, .formatted-message-buttons button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15); /* More pronounced shadow on hover */
        }
        .raw-data-buttons button:active, .formatted-message-buttons button:active {
            transform: translateY(0);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        /* Specific Button Colors and Styles */
        /* Format Button */
        .format-btn {
            background: #2c7be5; /* Primary blue */
            color: #ffffff;
        }
        .format-btn:hover {
            background-color: #246bbd;
        }

        /* Reset Button */
        .reset-btn {
            background: #dc3545; /* Red for reset */
            color: #ffffff;
        }
        .reset-btn:hover {
            background-color: #c82333;
        }

        /* Copy Button */
        .copy-btn {
            background: #28a745; /* Success green */
            color: #ffffff;
        }
        .copy-btn:hover {
            background-color: #218838;
        }

        /* Save Button */
        .save-btn {
            background: #ffc107; /* Warning yellow */
            color: #333333; /* Darker text for contrast */
            font-weight: 700; /* Bolder text for visibility */
        }
        .save-btn:hover {
            background-color: #e0a800;
        }


        /* Status Messages */
        .status-message {
            text-align: center;
            padding: 16px; /* More padding */
            margin: 25px 0; /* More margin */
            border-radius: 12px;
            font-weight: bold;
            display: none;
            animation: fadeIn 0.5s forwards;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #ffcdd2;
            color: #d32f2f;
            border: 1px solid #ef9a9a;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        /* Removed .tick as its functionality is now absorbed by .status-message */


        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-15px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive Adjustments */
        @media (max-width: 1024px) {
            .form-row.top-inputs .form-group {
                flex-basis: calc(50% - 12.5px); /* 2 items per row, adjusted for 25px gap */
            }
            .raw-data-section .form-group,
            .formatted-message-section .form-group {
                min-width: 280px; /* Slightly adjust min-width for medium screens */
            }
            /* Adjust button column width for medium screens */
            .raw-data-buttons, .formatted-message-buttons {
                width: 160px; /* Slightly narrower button column */
            }
        }

        @media (max-width: 768px) {
            body {
                padding-left: 15px; /* Reduced horizontal padding */
                padding-right: 15px; /* Reduced horizontal padding */
                padding-top: 60px; /* Adjust padding-top for smaller navbar height */
            }
            .navbar {
                padding: 10px 15px;
                /* On small screens, title and menu icon are first, links will be toggled */
                justify-content: space-between;
            }
            .navbar .site-title {
                font-size: 1.7rem;
                margin-right: 0; /* No margin on smaller screens */
            }
            .menu-icon {
                display: block; /* Show hamburger icon */
            }
            .navbar-links {
                display: none; /* Hide links by default */
                flex-direction: column; /* Stack links vertically */
                width: 100%;
                background: #2c7be5; /* Match navbar background */
                position: absolute; /* Position relative to navbar */
                top: 100%; /* Place below the navbar */
                left: 0;
                padding: 10px 0; /* Add padding */
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                border-top: 1px solid rgba(255,255,255,0.2);
                max-height: 0; /* Start hidden */
                overflow: hidden; /* Hide overflow */
                transition: max-height 0.3s ease-out, padding 0.3s ease-out; /* Smooth transition */
            }
            .navbar-links.active {
                display: flex; /* Show when active */
                max-height: 200px; /* Arbitrary max-height for smooth transition */
                padding: 15px 0; /* More padding when open */
            }
            .navbar-links a {
                padding: 10px 20px; /* Adjust link padding for vertical menu */
                width: calc(100% - 40px); /* Account for padding */
                border-radius: 0; /* No pill shape in vertical menu */
                background-color: transparent; /* Transparent background */
                text-align: center;
            }
            .navbar-links a:hover {
                background-color: rgba(255, 255, 255, 0.1);
                transform: none; /* No vertical transform on hover */
                box-shadow: none;
            }

            .container {
                padding: 25px; /* Reduced inner padding */
                border-radius: 15px; /* Keep some radius on mobile */
                margin-top: 0; /* No margin-top needed */
                margin-bottom: 20px; /* Adjusted bottom margin */
                max-width: none; /* Allow full width */
            }
            #inputData {
                min-height: 90px; /* Smaller height for mobile */
            }
            #formattedMessage {
                min-height: 90px; /* Smaller height for mobile */
            }
            .form-row {
                flex-direction: column; /* Force single column */
                gap: 15px;
                margin-bottom: 15px;
            }
            .form-group {
                min-width: unset;
                width: 100%;
                flex-basis: auto;
            }
            .form-row.top-inputs .form-group {
                flex-basis: auto;
            }

            /* Raw Data and Formatted Message Sections on small screens - stack vertically */
            .raw-data-section, .formatted-message-section {
                flex-direction: column;
                align-items: stretch; /* Stretch to full width */
                gap: 15px; /* Reduced gap when stacking */
            }
            .raw-data-section .form-group,
            .formatted-message-section .form-group {
                min-width: unset;
                width: 100%;
            }
            .raw-data-buttons, .formatted-message-buttons {
                flex-direction: row; /* Buttons side-by-side on small screens */
                justify-content: space-around;
                align-self: center; /* Center the button group */
                width: 100%;
                gap: 10px; /* Small gap when buttons are horizontal */
                height: auto; /* Allow button container to adapt height */
                width: 100%; /* Ensure button group takes full width */
            }
            .raw-data-buttons button,
            .formatted-message-buttons button {
                flex: 1; /* Make buttons share space equally */
                min-width: unset;
                padding: 10px; /* Adjust padding */
                font-size: 0.9rem;
                height: auto; /* Allow buttons to adjust height automatically on small screens */
                border-radius: 6px; /* Slightly less rounded */
            }

            label {
                font-size: 1rem;
                margin-bottom: 6px;
            }
            textarea, input[type="text"], input[type="date"], input[type="number"], input[type="email"], select {
                padding: 10px;
                font-size: 0.95rem;
                margin-bottom: 10px;
                border-radius: 8px;
            }
            .status-message {
                padding: 12px;
                margin: 15px 0;
            }
            .tick {
                font-size: 1.2rem;
                margin-left: 8px;
            }
        }

        @media (max-width: 480px) {
            body {
                padding-left: 10px;
                padding-right: 10px;
                padding-top: 55px;
            }
            .navbar { padding: 8px 12px; }
            .navbar .site-title { font-size: 1.5rem; }
            .menu-icon { font-size: 1.8rem; }
            .container { padding: 18px; }
            textarea, input[type="text"], input[type="date"], input[type="number"], input[type="email"], select {
                padding: 8px;
                font-size: 0.85rem;
            }
            .raw-data-buttons button,
            .formatted-message-buttons button {
                padding: 8px 12px;
                font-size: 0.8rem;
            }
            .status-message {
                padding: 10px;
                margin: 10px 0;
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="navbar">
        <span class="site-title">MJ Hauling United LLC</span>
        <div class="menu-icon" onclick="toggleMenu()">&#9776;</div>
        <div class="navbar-links" id="navbarLinks">
            <a href="view_leads.php">View All Leads</a>
            <?php if ($is_admin_logged_in): ?>
                <a href="admin.php">Admin Dashboard</a>
            <?php elseif ($is_user_logged_in): ?>
                <?php endif; ?>
            <a href="shippment_lead.php?logout=true">Logout</a>
        </div>
    </div>

    <div class="container">
        <div id="statusMessage" class="status-message"></div>

        <div class="form-row top-inputs">
            <div class="form-group">
                <label for="quoteAmountInput"><strong>Quote Amount ($):</strong></label>
                <input type="number" id="quoteAmountInput" placeholder="e.g., 655" step="0.01" required />
            </div>
            <div class="form-group">
                <label for="quoteDate"><strong>Quote Date (Compulsory):</strong></label>
                <input type="date" id="quoteDate" required />
            </div>
            <div class="form-group">
                <label for="shippmentDate"><strong>Shipment Date (Optional):</strong></label>
                <input type="date" id="shippmentDate" />
            </div>
            <div class="form-group">
                <label for="status"><strong>Status (Optional):</strong></label>
                <select id="status">
                    <option value="">-- Select Status --</option>
                    <option value="Booked">Booked</option>
                    <option value="Not Pick">Not Pick</option>
                    <option value="Voice Mail">Voice Mail</option>
                    <option value="In Future Shipment">In Future Shipment</option>
                    <option value="Qutation">Qutation</option>
                    <option value="Invalid Lead">Invalid Lead</option>
                    <option value="Stop Lead">Stop Lead</option>
                    <option value="Delivered">Delivered</option>
                    <option value="Already Booked">Already Booked</option>
                </select>
            </div>
        </div>

        <div class="raw-data-section">
            <div class="form-group raw-data">
                <label for="inputData"><strong>Paste Raw Lead Data (Optional):</strong></label>
                <textarea id="inputData" placeholder="Paste your raw lead here. Information like Name, Email, Phone, Vehicle details, and locations will be extracted.
Example Format:

Ship Date: 2025-07-30
CONTACT INFORMATION
Name: James M Martinez
Email Address: <EMAIL>
Phone: 9707785197
VEHICLE INFORMATION
Year: 1949
Make: CHEVROLET
Model: FLEETLINE
Type: Coupe
PICKUP AND DELIVERY INFORMATION
Pickup City: APACHE JUNCTION
Pickup State: AZ
Pickup Zipcode: 81501
Delivery City: GRAND JUNCTION
Delivery State: CO
Delivery Zipcode: 81501"></textarea>
            </div>
            <div class="raw-data-buttons">
                <button class="format-btn" onclick="formatLead()">Format Message</button>
                <button class="reset-btn" onclick="resetForm()">Reset Form</button>
            </div>
        </div>

        <div class="formatted-message-section">
            <div class="form-group full-width">
                <label for="formattedMessage"><strong>Formatted Message:</strong></label>
                <textarea id="formattedMessage" placeholder="Formatted message will appear here..." readonly></textarea>
            </div>
            <div class="formatted-message-buttons">
                <button class="copy-btn" onclick="copyMessage()">Copy to Clipboard</button>
                <button class="save-btn" onclick="saveToDatabase()">Save to Database</button>
            </div>
            </div>

    </div>

    <form id="dataForm" action="safe_lead.php" method="post" style="display:none;">
        <input type="hidden" name="name" id="dbName">
        <input type="hidden" name="email" id="dbEmail">
        <input type="hidden" name="phone" id="dbPhone">
        <input type="hidden" name="quote_amount" id="dbQuoteAmount">
        <input type="hidden" name="quote_id" id="dbQuoteID">
        <input type="hidden" name="quote_date" id="dbQuoteDate">
        <input type="hidden" name="shippment_date" id="dbShippmentDate">
        <input type="hidden" name="status" id="dbStatus">
        <input type="hidden" name="year" id="dbYear">
        <input type="hidden" name="make" id="dbMake">
        <input type="hidden" name="model" id="dbModel">
        <input type="hidden" name="pickup_city" id="dbPickupCity">
        <input type="hidden" name="pickup_state" id="dbPickupState">
        <input type="hidden" name="pickup_zip" id="dbPickupZip">
        <input type="hidden" name="delivery_city" id="dbDeliveryCity">
        <input type="hidden" name="delivery_state" id="dbDeliveryState">
        <input type="hidden" name="delivery_zip" id="dbDeliveryZip">
        <input type="hidden" name="formatted_message" id="dbFormattedMessage">
        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
        <input type="hidden" name="user_id" value="<?php echo htmlspecialchars($logged_in_user_id ?? $logged_in_admin_id); ?>">
    </form>

    <footer>Powered by Desired Technologies</footer>

    <script>
        // Toggles the visibility of the navigation menu on smaller screens
        function toggleMenu() {
            const navbarLinks = document.getElementById('navbarLinks');
            navbarLinks.classList.toggle('active');
        }

        // Displays status messages (success/error/warning) to the user
        function displayStatusMessage(message, type, elementToFocus = null) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.textContent = message;
            statusDiv.className = `status-message ${type}`; // Apply styling
            statusDiv.style.display = 'block'; // Make visible
            if (elementToFocus) {
                elementToFocus.focus(); // Focus on the problematic input field
            }
            setTimeout(() => statusDiv.style.display = 'none', 5000); // Hide after 5 seconds
        }

        // Converts string to Title Case (e.g., "john doe" to "John Doe")
        function toTitleCase(str) {
            return str?.toLowerCase().replace(/\b\w/g, l => l.toUpperCase()) || '';
        }

        // Sets the default date for the quoteDate input to today's date
        function setTodayDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            document.getElementById('quoteDate').value = `${year}-${month}-${day}`;
        }

        // This function runs automatically when the page finishes loading
        window.onload = function () {
            // Check for status messages passed via URL parameters from save_lead.php
            const urlParams = new URLSearchParams(window.location.search);
            const status = urlParams.get('status');
            const message = urlParams.get('message');
            if (status && message) {
                displayStatusMessage(decodeURIComponent(message), status);
            }
            // Auto-populate the Quote Date field with the current date
            setTodayDate();
        };

        // Resets all form fields to their initial state
        function resetForm() {
            document.getElementById('inputData').value = '';
            document.getElementById('quoteAmountInput').value = '';
            document.getElementById('shippmentDate').value = '';
            document.getElementById('status').value = '';
            document.getElementById('formattedMessage').value = '';

            setTodayDate(); // Reset auto-populated quote date to today

            // Clear all hidden form fields
            document.getElementById('dbName').value = '';
            document.getElementById('dbEmail').value = '';
            document.getElementById('dbPhone').value = '';
            document.getElementById('dbQuoteAmount').value = '';
            document.getElementById('dbQuoteID').value = '';
            document.getElementById('dbQuoteDate').value = '';
            document.getElementById('dbShippmentDate').value = '';
            document.getElementById('dbStatus').value = '';
            document.getElementById('dbYear').value = '';
            document.getElementById('dbMake').value = '';
            document.getElementById('dbModel').value = '';
            document.getElementById('dbPickupCity').value = '';
            document.getElementById('dbPickupState').value = '';
            document.getElementById('dbPickupZip').value = '';
            document.getElementById('dbDeliveryCity').value = '';
            document.getElementById('dbDeliveryState').value = '';
            document.getElementById('dbDeliveryZip').value = '';
            document.getElementById('dbFormattedMessage').value = '';

            document.getElementById('inputData').focus();
            displayStatusMessage('Form reset successfully!', 'success');
        }

        // Extracts data from raw input and visible fields, then formats the message.
        // Also populates the hidden fields of the submission form.
        function extractAndFormatData() {
            const input = document.getElementById('inputData').value;
            const quoteDate = document.getElementById('quoteDate').value || '';
            const shippmentDate = document.getElementById('shippmentDate').value || '';
            const status = document.getElementById('status').value || '';
            const quote = parseFloat(document.getElementById('quoteAmountInput').value) || 0;
            const sanitize = str => str?.trim() ?? '';

            let finalName = sanitize(
                input.match(/(?:Name|Customer Name|Client Name|Customer):\s*(.*)/i)?.[1]
            );

            let extractedPhone = sanitize(input.match(/(?:Phone|Contact #|Tel):\s*(.*)/i)?.[1]);

            let quoteID = '';
            if (extractedPhone) {
                const digitsOnly = extractedPhone.replace(/\D/g, '');
                if (digitsOnly.length >= 4) {
                    quoteID = digitsOnly.slice(-4);
                } else {
                    quoteID = digitsOnly;
                    displayStatusMessage('Warning: Phone number has less than 4 digits, Quote ID might be incomplete.', 'warning', document.getElementById('inputData'));
                }
            } else {
                displayStatusMessage('Warning: Phone number not found in raw data. Quote ID will be "N/A".', 'warning', document.getElementById('inputData'));
            }

            if (!finalName && input.trim() !== '') { // Only show error if raw data is not empty
                displayStatusMessage('Customer Name could not be extracted from raw data. Please ensure "Name: [Customer Name]" is present.', 'error', document.getElementById('inputData'));
            }

            let extractedEmail = sanitize(input.match(/(?:Email|E-mail|Email Address):\s*(.*)/i)?.[1]);

            const year = sanitize(input.match(/(?:Year|Vehicle Year):\s*(.*)/i)?.[1]);
            const make = sanitize(input.match(/(?:Make|Vehicle Make):\s*(.*)/i)?.[1]);
            const model = sanitize(input.match(/(?:Model|Vehicle Model):\s*(.*)/i)?.[1]);
            const pickupCity = toTitleCase(sanitize(input.match(/(?:Pickup City|Origin City|From City):\s*(.*)/i)?.[1]));
            const pickupState = sanitize(input.match(/(?:Pickup State|Origin State|From State):\s*(.*)/i)?.[1]?.toUpperCase());
            const pickupZip = sanitize(input.match(/(?:Pickup Zipcode|Origin Zip|From Zip):\s*(.*)/i)?.[1]);
            const deliveryCity = toTitleCase(sanitize(input.match(/(?:Delivery City|Destination City|To City):\s*(.*)/i)?.[1]));
            const deliveryState = sanitize(sanitize(input.match(/(?:Delivery State|Destination State|To City):\s*(.*)/i)?.[1])?.toUpperCase());
            const deliveryZip = sanitize(input.match(/(?:Delivery Zipcode|Destination Zip|To Zip):\s*(.*)/i)?.[1]);

            const formattedQuoteID = quoteID ? (quoteID.startsWith('MJH-') ? quoteID : `MJH-${quoteID}`) : 'N/A';

            const message = `Good day! ${finalName || 'Customer'},
I'm pleased to give you the quotation of $${quote.toFixed(2)} for shipping your vehicle having Quote ID: ${formattedQuoteID} (Date: ${quoteDate}).
Year: ${year || 'N/A'}
Make: ${make || 'N/A'}
Model: ${model || 'N/A'}
Origin: ${pickupCity || 'N/A'}, ${pickupState || 'N/A'} ${pickupZip || 'N/A'}
Destination: ${deliveryCity || 'N/A'}, ${deliveryState || 'N/A'} ${deliveryZip || 'N/A'}
• Bumper to Bumper insurance up to $250,000
• 150 lbs of personal belongings included
• Door-to-Door Shipment
• Live Tracking Available
Mathew Wade
Call or Text: +1‪‪(*************‬
MJ Hauling United LLC
Text "Stop" to opt out`;

            document.getElementById('formattedMessage').value = message;

            document.getElementById('dbName').value = finalName;
            document.getElementById('dbEmail').value = extractedEmail;
            document.getElementById('dbPhone').value = extractedPhone;
            document.getElementById('dbQuoteAmount').value = quote;
            document.getElementById('dbQuoteID').value = formattedQuoteID; // Use formatted quote ID for consistency
            document.getElementById('dbQuoteDate').value = quoteDate;
            document.getElementById('dbShippmentDate').value = shippmentDate;
            document.getElementById('dbStatus').value = status;
            document.getElementById('dbYear').value = year;
            document.getElementById('dbMake').value = make;
            document.getElementById('dbModel').value = model;
            document.getElementById('dbPickupCity').value = pickupCity;
            document.getElementById('dbPickupState').value = pickupState;
            document.getElementById('dbPickupZip').value = pickupZip;
            document.getElementById('dbDeliveryCity').value = deliveryCity;
            document.getElementById('dbDeliveryState').value = deliveryState;
            document.getElementById('dbDeliveryZip').value = deliveryZip;
            document.getElementById('dbFormattedMessage').value = message;

            return quote !== 0 && quoteDate; // Name and phone are no longer strictly required for formatting but will affect "N/A"
        }

        // Formats the lead message and selects it for easy copying.
        function formatLead() {
            const isValid = extractAndFormatData();

            const msgBox = document.getElementById('formattedMessage');
            msgBox.focus();
            msgBox.select();

            if (isValid) {
                displayStatusMessage('Message formatted and data prepared!', 'success');
            }
        }

        // Performs client-side validation for mandatory fields and then submits the form.
        function saveToDatabase() {
            // Ensure data is extracted and populated before validation
            extractAndFormatData();

            const dbName = document.getElementById('dbName').value.trim();
            const dbQuoteAmount = document.getElementById('dbQuoteAmount').value.trim();
            const dbQuoteID = document.getElementById('dbQuoteID').value.trim();
            const dbQuoteDate = document.getElementById('dbQuoteDate').value;

            if (!dbName) {
                displayStatusMessage('Customer Name is missing. Please ensure "Name: [Customer Name]" is in the Raw Lead Data.', 'error', document.getElementById('inputData'));
                return;
            }
            if (!dbQuoteAmount || isNaN(parseFloat(dbQuoteAmount))) {
                displayStatusMessage('Quote Amount is missing or invalid. Please enter a valid number in the "Quote Amount ($)" field.', 'error', document.getElementById('quoteAmountInput'));
                return;
            }
            if (!dbQuoteID || dbQuoteID === 'N/A') {
                displayStatusMessage('Quote ID could not be derived from Phone number. Please ensure "Phone: [Number]" is present in Raw Lead Data and has at least 4 digits.', 'error', document.getElementById('inputData'));
                return;
            }
            if (!dbQuoteDate) {
                displayStatusMessage('Quote Date is required. Please select a date.', 'error', document.getElementById('quoteDate'));
                return;
            }

            // If all client-side validations pass, submit the form
            document.getElementById('dataForm').submit();
        }

        // Copies the content of the formatted message textarea to the clipboard
        function copyMessage() {
            const msg = document.getElementById('formattedMessage');
            navigator.clipboard.writeText(msg.value).then(() => {
                displayStatusMessage('Copied to Clipboard! ✅', 'success');
            }).catch(err => {
                console.error('Failed to copy text: ', err);
                displayStatusMessage('Failed to copy to clipboard. Please copy manually.', 'error');
            });
        }

        // Shows a temporary "✅ Copied!" message next to the copy button
        function showTick() {
            // This function is no longer directly used for copy feedback as displayStatusMessage handles it.
            // Keeping it here for reference in case it's used elsewhere or you change your mind.
            const tick = document.getElementById("tick");
            if (tick) {
                tick.style.display = "inline";
                setTimeout(() => tick.style.display = "none", 2000);
            }
        }

        // Event listener for Ctrl/Cmd + Enter on raw data textarea
        document.getElementById('inputData').addEventListener('keydown', function(e) {
             if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                 e.preventDefault();
                 formatLead();
             }
        });

        // Event listener for Ctrl/Cmd + C on formatted message textarea
        document.getElementById('formattedMessage').addEventListener('keydown', function(e) {
            if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'c') {
                navigator.clipboard.writeText(this.value).then(() => {
                    displayStatusMessage('Copied to Clipboard! ✅', 'success');
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                    displayStatusMessage('Failed to copy to clipboard. Please copy manually.', 'error');
                });
            }
        });
    </script>
</body>
</html>