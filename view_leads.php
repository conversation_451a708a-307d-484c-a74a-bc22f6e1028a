<?php

session_start();

// Redirect function for clean redirects
function redirectWithStatus($page, $status, $message) {
    header('Location: ' . $page . '?status=' . urlencode($status) . '&message=' . urlencode($message));
    exit();
}

// Include PHPMailer classes and email configuration
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;

// Load email configuration
$email_config = require 'email_config.php';

// Include PHPMailer files
require 'PHPMailer/src/Exception.php';
require 'PHPMailer/src/PHPMailer.php';
require 'PHPMailer/src/SMTP.php';

/**
 * Function to send email using PHPMailer with proper configuration
 */
function sendEmail($to_email, $subject, $message_body, $from_email = null, $from_name = null) {
    global $email_config;

    $mail = new PHPMailer(true);

    try {
        // Use config file settings or defaults
        $smtp_from_email = $from_email ?: $email_config['from_email'];
        $smtp_from_name = $from_name ?: $email_config['from_name'];

        // Server settings
        if ($email_config['debug_mode']) {
            $mail->SMTPDebug = SMTP::DEBUG_SERVER;
        }

        $mail->isSMTP();
        $mail->Host       = $email_config['smtp_host'];
        $mail->SMTPAuth   = $email_config['smtp_auth'];
        $mail->Username   = $email_config['smtp_username'];
        $mail->Password   = $email_config['smtp_password'];
        $mail->SMTPSecure = $email_config['smtp_secure'];
        $mail->Port       = $email_config['smtp_port'];
        $mail->CharSet    = $email_config['charset'];

        // Recipients
        $mail->setFrom($smtp_from_email, $smtp_from_name);
        $mail->addAddress($to_email);
        $mail->addReplyTo($smtp_from_email, $smtp_from_name);

        // Content
        $mail->isHTML($email_config['use_html']);
        $mail->Subject = $subject;

        if ($email_config['use_html']) {
            $mail->Body = nl2br(htmlspecialchars($message_body));
            $mail->AltBody = htmlspecialchars($message_body);
        } else {
            $mail->Body = htmlspecialchars($message_body);
        }

        $mail->send();
        return ['success' => true, 'message' => 'Email sent successfully!'];

    } catch (Exception $e) {
        error_log("PHPMailer Error: " . $mail->ErrorInfo);
        return ['success' => false, 'message' => "Email could not be sent. Error: {$mail->ErrorInfo}"];
    }
}

// Auto-logout after 90 minutes (5400 seconds) of inactivity
$inactivity_timeout = 5400; // 90 minutes * 60 seconds/minute

// Check current login status and get user IDs
$is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$is_user_logged_in = isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;

$logged_in_admin_id = $_SESSION['admin_id'] ?? null; // Assuming admin_id is set upon admin login
$logged_in_user_id = $_SESSION['user_id'] ?? null;   // Assuming user_id is set upon user login

// --- Handle Logout request first ---
if (isset($_GET['logout']) && $_GET['logout'] == 'true') {
    $logged_out_type = $_SESSION['logged_in_type'] ?? 'user'; // Default to user logout page
    session_unset();    // Unset all session variables
    session_destroy(); // Destroy the session

    // Redirect to the appropriate login page after logout
    if ($logged_out_type === 'admin') {
        redirectWithStatus('admin.php', 'success', 'You have been logged out.');
    } else {
        redirectWithStatus('user_login.php', 'success', 'You have been logged out.');
    }
}

// --- Enforce Login for Shared Pages ---
// If NEITHER admin NOR local user is logged in, redirect to user_login.php
if (!$is_admin_logged_in && !$is_user_logged_in) {
    redirectWithStatus('user_login.php', 'error', 'Please log in to access this page.');
}

// --- Auto-logout check for ACTIVE session (either admin or user) ---
if (($is_admin_logged_in || $is_user_logged_in) && isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > $inactivity_timeout)) {
    $logged_out_type = $_SESSION['logged_in_type'] ?? 'user'; // Capture type before unsetting
    session_unset();
    session_destroy();
    if ($logged_out_type === 'admin') {
        redirectWithStatus('admin.php', 'error', 'You were logged out due to inactivity.');
    } else {
        redirectWithStatus('user_login.php', 'error', 'You were logged out due to inactivity.');
    }
}

// Update last activity time and store user type (Crucial for auto-logout redirect)
// Only update if someone is actively logged in
if ($is_admin_logged_in) {
    $_SESSION['last_activity'] = time();
    $_SESSION['logged_in_type'] = 'admin';
} elseif ($is_user_logged_in) {
    $_SESSION['last_activity'] = time();
    $_SESSION['logged_in_type'] = 'user';
}
// --- END AUTHENTICATION LOGIC ---

// Generate CSRF token for form submission (after authentication check)
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Handle email sending request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_email'])) {
    // Check CSRF token
    if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        redirectWithStatus('view_leads.php', 'error', 'Invalid security token. Please try again.');
    }

    $to_email = filter_input(INPUT_POST, 'to_email', FILTER_SANITIZE_EMAIL);
    $from_email = filter_input(INPUT_POST, 'from_email', FILTER_SANITIZE_EMAIL);
    $subject = filter_input(INPUT_POST, 'subject', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $message_body = filter_input(INPUT_POST, 'message_body', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $customer_name = filter_input(INPUT_POST, 'customer_name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

    // Validation
    if (empty($to_email) || !filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
        redirectWithStatus('view_leads.php', 'error', 'Error: Valid recipient email is required.');
    }

    if (empty($subject) || empty($message_body)) {
        redirectWithStatus('view_leads.php', 'error', 'Error: Subject and message are required.');
    }

    // Prepare subject with customer name
    $full_subject = "[MJ Hauling] " . $subject;
    if (!empty($customer_name)) {
        $full_subject = "[MJ Hauling] Message for " . $customer_name . " - " . $subject;
    }

    // Send email using the function
    $result = sendEmail($to_email, $full_subject, $message_body, $from_email);

    if ($result['success']) {
        redirectWithStatus('view_leads.php', 'success', 'Email sent successfully to ' . htmlspecialchars($customer_name ?: $to_email) . '!');
    } else {
        redirectWithStatus('view_leads.php', 'error', $result['message']);
    }
}

// Database configuration (same as in save_lead.php)
$db_host = 'localhost';
$db_name = 'dbnkkk8lxffmdu';
$db_user = 'uihxynu3jgkt9';
$db_pass = '@:5`|lt+1f1@';

// Establish database connection
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // In a production environment, you would log this error and show a generic message.
    die("Database connection failed: " . $e->getMessage());
}

// Handle search and filter parameters
$search_query = $_GET['search_query'] ?? '';
$filter_ship_date = $_GET['filter_ship_date'] ?? '';
$filter_status = $_GET['filter_status'] ?? 'All'; // Get status filter, default to 'All'
$filter_start_date = $_GET['filter_start_date'] ?? '';
$filter_end_date = $_GET['filter_end_date'] ?? '';

// Fetch leads from the database with search and filter, *respecting user permissions*
$leads = [];
try {
    // Start building the query
    $sql = "SELECT *,
                    DATE_FORMAT(shippment_lead.shippment_date, '%Y-%m-%d') AS formatted_shippment_date,
                    DATE_FORMAT(shippment_lead.quote_date, '%Y-%m-%d') AS formatted_quote_date,
                    DATE_FORMAT(shippment_lead.created_at, '%Y-%m-%d %H:%i:%s') AS formatted_created_at
            FROM shippment_lead WHERE 1=1";

    $params = [];

    // --- IMPORTANT: Add user-specific filter here ---
    if ($is_user_logged_in && $logged_in_user_id !== null) {
        // If a regular user is logged in, show only their leads.
        // ASSUMPTION: shippment_lead table has a 'user_id' column
        $sql .= " AND shippment_lead.user_id = :current_user_id";
        $params[':current_user_id'] = $logged_in_user_id;
    }
    // If an admin is logged in, no additional user_id filter is applied,
    // so they will see all leads (as per the default behavior).

    // Add general search query conditions for specified fields
    if (!empty($search_query)) {
        // Build the OR conditions for each searchable field
        $search_conditions = [];
        $search_value = '%' . htmlspecialchars($search_query) . '%'; // Prepare search term once

        $search_conditions[] = "CAST(shippment_lead.id AS CHAR) LIKE :search_query_id";
        $params[':search_query_id'] = $search_value;

        $search_conditions[] = "shippment_lead.name LIKE :search_query_name";
        $params[':search_query_name'] = $search_value;

        $search_conditions[] = "shippment_lead.email LIKE :search_query_email";
        $params[':search_query_email'] = $search_value;

        $search_conditions[] = "shippment_lead.phone LIKE :search_query_phone";
        $params[':search_query_phone'] = $search_value;

        $search_conditions[] = "CAST(shippment_lead.quote_amount AS CHAR) LIKE :search_query_quote_amount";
        $params[':search_query_quote_amount'] = $search_value;

        $search_conditions[] = "DATE_FORMAT(shippment_lead.shippment_date, '%Y-%m-%d') LIKE :search_query_shippment_date";
        $params[':search_query_shippment_date'] = $search_value;

        $search_conditions[] = "shippment_lead.status LIKE :search_query_status";
        $params[':search_query_status'] = $search_value;

        $search_conditions[] = "shippment_lead.quote_id LIKE :search_query_quote_id";
        $params[':search_query_quote_id'] = $search_value;

        // Combine all search conditions with OR
        $sql .= " AND (" . implode(" OR ", $search_conditions) . ")";
    }

    // Add shipment date filter - prioritize exact date over range
    if (!empty($filter_ship_date)) {
        // If exact date is specified, use it and ignore date range
        $sql .= " AND shippment_lead.shippment_date = :filter_ship_date";
        $params[':filter_ship_date'] = htmlspecialchars($filter_ship_date);
    } else {
        // Only use date range if exact date is not specified
        if (!empty($filter_start_date) && !empty($filter_end_date)) {
            $sql .= " AND shippment_lead.shippment_date BETWEEN :filter_start_date AND :filter_end_date";
            $params[':filter_start_date'] = htmlspecialchars($filter_start_date);
            $params[':filter_end_date'] = htmlspecialchars($filter_end_date);
        } elseif (!empty($filter_start_date)) {
            $sql .= " AND shippment_lead.shippment_date >= :filter_start_date";
            $params[':filter_start_date'] = htmlspecialchars($filter_start_date);
        } elseif (!empty($filter_end_date)) {
            $sql .= " AND shippment_lead.shippment_date <= :filter_end_date";
            $params[':filter_end_date'] = htmlspecialchars($filter_end_date);
        }
    }

    // New: Add status filter condition
    if (!empty($filter_status) && $filter_status !== 'All') {
        $sql .= " AND shippment_lead.status = :filter_status";
        $params[':filter_status'] = htmlspecialchars($filter_status);
    }

    // Order by created_at (time of insertion) for most recent at top
    $sql .= " ORDER BY shippment_lead.created_at DESC, shippment_lead.id DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $leads = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    die("Error fetching leads: " . $e->getMessage());
}

// Handle success/error messages from other pages (e.g., update_lead.php, delete_lead.php)
$status_message = '';
$status_type = '';
if (isset($_GET['status']) && isset($_GET['message'])) {
    $status_type = htmlspecialchars($_GET['status']);
    $status_message = htmlspecialchars(urldecode($_GET['message']));
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View All Leads - MJ Hauling United LLC</title>
    <style>
        :root {
            --primary-color: #2c73d2;
            --primary-dark: #1a4b8c;
            --secondary-color: #28a745;
            --danger-color: #d9534f;
            --danger-dark: #c9302c;
            --gray-color: #6c757d;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white: #ffffff;
            --shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            padding: 0;
            margin: 0;
            color: #333;
            padding-top: 70px;
            line-height: 1.6;
            overflow-x: hidden;
            min-width: 320px;
        }

        /* Prevent horizontal scrolling on small screens */
        * {
            box-sizing: border-box;
        }

        /* Ensure navbar doesn't cause horizontal scroll */
        .navbar * {
            box-sizing: border-box;
        }

        /* --- Navbar Styles --- */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            padding: 0 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--shadow);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            box-sizing: border-box;
            height: 70px;
            min-height: 70px;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            height: 100%;
            flex-shrink: 0;
            z-index: 1001;
        }

        .navbar-logo {
            height: 45px;
            width: auto;
            object-fit: contain;
            margin-right: 10px;
        }

        .navbar-container {
            display: flex;
            height: 100%;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            max-width: none;
            overflow: hidden;
        }

        .navbar-nav {
            display: flex;
            align-items: center;
            height: 100%;
            margin: 0;
            padding: 0;
            list-style: none;
            flex-shrink: 0;
        }

        .nav-item {
            height: 100%;
            display: flex;
            align-items: center;
            position: relative;
        }

        .nav-link {
            color: var(--white);
            text-decoration: none;
            padding: 0 12px;
            height: 100%;
            display: flex;
            align-items: center;
            transition: var(--transition);
            font-weight: 500;
            white-space: nowrap;
            font-size: 0.9rem;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .nav-link i {
            margin-right: 6px;
            font-size: 1rem;
        }

        .logout-btn {
            background-color: var(--danger-color);
            color: var(--white);
            border: none;
            padding: 0 15px;
            height: 100%;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: var(--transition);
            font-weight: 500;
            font-size: 0.9rem;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .logout-btn:hover {
            background-color: var(--danger-dark);
        }

        .logout-btn i {
            margin-right: 6px;
        }

        .search-container {
            display: flex;
            align-items: center;
            flex: 1;
            max-width: 800px;
            margin: 0 15px;
            height: 100%;
            overflow: hidden;
        }

        .search-form {
            display: flex;
            gap: 8px;
            align-items: center;
            width: 100%;
            flex-wrap: nowrap;
            overflow-x: auto;
            padding: 5px 0;
        }
        
        .search-form input,
        .search-form select,
        .search-form button {
            padding: 6px 10px;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background-color: rgba(255, 255, 255, 0.95);
            transition: var(--transition);
            font-size: 0.85rem;
            height: 36px;
            box-sizing: border-box;
            flex-shrink: 0;
        }

        .search-form input[type="text"] {
            min-width: 140px;
            max-width: 180px;
        }

        .search-form input[type="date"] {
            min-width: 120px;
            max-width: 140px;
        }

        .search-form select {
            min-width: 100px;
            max-width: 130px;
        }

        .search-form input:focus,
        .search-form select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(44, 115, 210, 0.3);
        }

        .search-form button {
            background-color: var(--secondary-color);
            color: var(--white);
            border: none;
            cursor: pointer;
            font-weight: 500;
            padding: 6px 12px;
            white-space: nowrap;
            min-width: 80px;
            border-radius: 4px;
            transition: var(--transition);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .search-form button:hover {
            background-color: #218838;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .search-form .reset-btn {
            background-color: var(--gray-color);
            color: var(--white);
        }

        .search-form .reset-btn:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .button-group {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
        }

        .date-range-group {
            display: flex;
            align-items: center;
            gap: 6px;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            height: 36px;
            box-sizing: border-box;
            flex-shrink: 0;
        }

        .date-separator {
            color: #666;
            font-weight: 500;
            font-size: 0.8rem;
            white-space: nowrap;
            margin: 0 2px;
        }

        .date-range-group input[type="date"] {
            border: none;
            background: transparent;
            padding: 2px 4px;
            margin: 0;
            font-size: 0.8rem;
            width: 110px;
            height: 30px;
        }

        .filter-help-text {
            text-align: center;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.85rem;
        }

        /* --- Responsive Adjustments --- */
        @media (max-width: 1400px) {
            .search-container {
                max-width: 700px;
            }

            .search-form {
                gap: 6px;
            }

            .search-form input[type="text"] {
                min-width: 120px;
                max-width: 150px;
            }

            .search-form input[type="date"] {
                min-width: 110px;
                max-width: 130px;
            }

            .date-range-group input[type="date"] {
                width: 100px;
            }
        }

        @media (max-width: 1200px) {
            .navbar {
                padding: 0 10px;
            }

            .search-container {
                max-width: 600px;
                margin: 0 10px;
            }

            .search-form {
                gap: 5px;
            }

            .search-form input[type="text"] {
                min-width: 100px;
                max-width: 130px;
            }

            .search-form input[type="date"] {
                min-width: 100px;
                max-width: 120px;
            }

            .search-form select {
                min-width: 90px;
                max-width: 110px;
            }

            .date-range-group input[type="date"] {
                width: 90px;
            }

            .search-form button {
                padding: 6px 8px;
                min-width: 70px;
            }
        }

        @media (max-width: 992px) {
            .navbar {
                flex-wrap: wrap;
                height: auto;
                min-height: 70px;
                padding: 10px 15px;
            }

            .navbar-container {
                width: 100%;
                flex-direction: column;
                gap: 15px;
                position: static;
                background: none;
                box-shadow: none;
                max-height: none;
                overflow: visible;
            }

            .navbar-nav {
                flex-direction: row;
                width: auto;
                height: auto;
                justify-content: center;
                flex-wrap: wrap;
                gap: 10px;
            }

            .nav-item {
                width: auto;
                height: auto;
            }

            .nav-link {
                padding: 8px 16px;
                width: auto;
                justify-content: center;
                border: 1px solid rgba(255,255,255,0.3);
                border-radius: 6px;
                background-color: rgba(255,255,255,0.1);
                transition: all 0.3s ease;
            }

            .nav-link:hover {
                background-color: rgba(255,255,255,0.2);
                border-color: rgba(255,255,255,0.5);
            }

            .logout-btn {
                width: auto;
                padding: 8px 16px;
                justify-content: center;
                border-radius: 6px;
                margin: 0;
                align-self: center;
            }

            .search-container {
                width: 100%;
                padding: 0;
                margin: 0;
                max-width: none;
                border: none;
            }

            .search-form {
                width: 100%;
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .search-form input,
            .search-form select,
            .search-form button {
                width: 100%;
                max-width: none;
                min-width: auto;
            }

            .date-range-group {
                width: 100%;
                justify-content: space-between;
            }

            .date-range-group input[type="date"] {
                width: 45%;
            }

            .filter-help-text {
                margin-bottom: 10px;
                font-size: 0.8rem;
            }
        }

        @media (max-width: 768px) {
            .navbar {
                padding: 8px 10px;
            }

            .navbar-nav {
                gap: 8px;
            }

            .nav-link {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .logout-btn {
                padding: 6px 12px;
                font-size: 0.85rem;
            }

            .search-form {
                flex-wrap: wrap;
                justify-content: center;
            }

            .search-form input,
            .search-form select {
                flex: 1;
                min-width: 120px;
            }

            .date-range-group {
                flex: 1;
                min-width: 200px;
            }
        }

        @media (max-width: 576px) {
            .navbar {
                padding: 8px;
                min-height: 60px;
            }

            .navbar-logo {
                height: 35px;
            }

            .navbar-nav {
                gap: 6px;
                justify-content: flex-start;
            }

            .nav-link {
                padding: 5px 10px;
                font-size: 0.8rem;
            }

            .logout-btn {
                padding: 5px 10px;
                font-size: 0.8rem;
            }

            body {
                padding-top: 60px;
            }

            .search-form input,
            .search-form select,
            .search-form button {
                font-size: 0.8rem;
                padding: 8px 10px;
                height: 40px;
            }

            .date-range-group input[type="date"] {
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .navbar {
                padding: 6px;
            }

            .navbar-nav {
                flex-direction: column;
                width: 100%;
                gap: 5px;
            }

            .nav-link,
            .logout-btn {
                width: 100%;
                text-align: center;
                padding: 8px;
                font-size: 0.8rem;
            }

            .search-form {
                gap: 8px;
            }

            .search-form input[type="text"] {
                font-size: 0.75rem;
            }

            .date-range-group {
                flex-direction: column;
                gap: 5px;
                padding: 8px;
                height: auto;
            }

            .date-range-group input[type="date"] {
                width: 100%;
            }

            .date-separator {
                align-self: center;
                margin: 2px 0;
            }

            .filter-help-text {
                font-size: 0.75rem;
                line-height: 1.3;
            }
        }

        /* --- Main Container & Status Message --- */
        .container {
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
            width: 100%;
            max-width: 1400px;
            margin: 20px auto;
        }
        .status-message {
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            border-radius: 10px;
            font-weight: bold;
            display: <?php echo !empty($status_message) ? 'block' : 'none'; ?>;
            animation: fadeIn 0.5s forwards;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .success {
            background-color: #e6ffed;
            color: #1a7e3d;
            border: 1px solid #a8e6b9;
        }
        .error {
            background-color: #ffe6e6;
            color: #d63333;
            border: 1px solid #ffb3b3;
        }
        .warning {
            background-color: #fff8e6;
            color: #b3771a;
            border: 1px solid #ffe0b3;
        }

        /* --- Table Styles --- */
        .table-responsive-wrapper {
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: inset 0 0 5px rgba(0,0,0,0.02);
            margin-top: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            min-width: 800px;
        }
        th, td {
            border: 1px solid #eee;
            padding: 10px 12px;
            text-align: left;
            vertical-align: middle;
            word-wrap: break-word;
            white-space: normal;
        }
        th {
            background-color: #eef2f7;
            color: #4a5568;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8em;
            position: sticky;
            left: 0;
            z-index: 2;
        }
        tr:nth-child(even) {
            background-color: #f8fbfd;
        }
        tr:hover {
            background-color: #eef7ff;
            cursor: pointer;
        }
        td.actions {
            white-space: normal;
            min-width: 180px;
            text-align: center;
        }
        .actions a {
            margin-right: 8px;
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 5px;
            font-size: 0.85em;
            white-space: nowrap;
            display: inline-block;
            margin-bottom: 5px;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
        }
        .actions .edit-btn {
            background-color: #007bff;
            color: white;
        }
        .actions .edit-btn:hover {
            background-color: #0056b3;
            transform: translateY(-1px);
        }
        .actions .email-btn {
            background-color: #28a745;
            color: white;
            margin-left: 5px;
            border: none;
            padding: 6px 12px;
            border-radius: 5px;
            font-size: 0.85em;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.08);
            display: inline-block;
            margin-bottom: 5px;
            white-space: nowrap;
        }
        .actions .email-btn:hover {
            background-color: #218838;
            transform: translateY(-1px);
        }
        .actions .email-btn i {
            margin-right: 5px;
        }
        .no-leads {
            text-align: center;
            padding: 30px;
            font-style: italic;
            color: #777;
            background-color: #fefefe;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .total-entries {
            text-align: left;
            margin-top: 15px;
            margin-bottom: 10px;
            font-weight: bold;
            color: #555;
            padding-left: 5px;
        }
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        footer {
            text-align: center;
            margin-top: 4rem;
            font-weight: 600;
            color: #888;
            font-size: 0.9rem;
            padding-bottom: 20px;
        }

        /* --- Email Modal Styles --- */
        .modal {
            display: none;
            position: fixed;
            z-index: 1001;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.6);
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.3s forwards;
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            width: 90%;
            max-width: 600px;
            position: relative;
            animation: slideInTop 0.3s forwards;
        }

        .modal-content h3 {
            margin-top: 0;
            color: var(--primary-color);
            text-align: center;
            font-size: 1.8rem;
            margin-bottom: 25px;
        }

        .modal-content .form-group {
            margin-bottom: 20px;
        }

        .modal-content label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .modal-content input[type="text"],
        .modal-content input[type="email"],
        .modal-content select,
        .modal-content textarea {
            width: calc(100% - 24px);
            padding: 12px;
            border: 1px solid #c2dcfc;
            border-radius: 8px;
            font-size: 1rem;
            box-sizing: border-box;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .modal-content input:focus,
        .modal-content select:focus,
        .modal-content textarea:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(44, 115, 210, 0.2);
            outline: none;
        }

        .modal-content textarea {
            min-height: 120px;
            resize: vertical;
        }

        .modal-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 30px;
        }

        .modal-buttons button {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.2s ease;
            font-weight: 600;
        }

        .modal-buttons .send-btn {
            background-color: var(--secondary-color);
            color: white;
        }

        .modal-buttons .send-btn:hover {
            background-color: #218838;
            transform: translateY(-2px);
        }

        .modal-buttons .close-btn {
            background-color: var(--gray-color);
            color: white;
        }

        .modal-buttons .close-btn:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            position: absolute;
            top: 15px;
            right: 20px;
            cursor: pointer;
        }

        .close:hover,
        .close:focus {
            color: #000;
            text-decoration: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideInTop {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script>
        // Function to confirm deletion (kept for safety, though button is removed)
        function confirmDelete(id) {
            return confirm('Are you sure you want to delete this lead (ID: ' + id + ')? This action cannot be undone.');
        }



        // Function to display status messages
        window.onload = function() {
            const statusDiv = document.getElementById('statusMessage');
            if (statusDiv.style.display === 'block') {
                setTimeout(() => statusDiv.style.display = 'none', 5000);
            }


        };

        // Function to reset search and filter form
        function resetFilters() {
            window.location.href = 'view_leads.php';
        }

        // Email Modal Functions - Fixed Version
        function initializeEmailModal() {
            const emailModal = document.getElementById('emailModal');
            const emailButtons = document.querySelectorAll('.email-btn');
            const toEmailInput = document.getElementById('to_email');
            const customerNameHidden = document.getElementById('customer_name_hidden');
            const subjectInput = document.getElementById('subject');
            const messageBody = document.getElementById('message_body');
            const fromEmailSelect = document.getElementById('from_email');

            console.log('Email buttons found:', emailButtons.length); // Debug

            // Add event listeners to email buttons
            emailButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Email button clicked'); // Debug

                    const recipientEmail = this.getAttribute('data-email-to');
                    const customerName = this.getAttribute('data-customer-name') || '';
                    const leadId = this.getAttribute('data-lead-id') || '';

                    console.log('Email data:', recipientEmail, customerName, leadId); // Debug

                    // Populate modal fields
                    toEmailInput.value = recipientEmail;
                    customerNameHidden.value = customerName;

                    // Auto-select first available email option
                    if (fromEmailSelect.options.length > 1) {
                        fromEmailSelect.selectedIndex = 1; // Select first real option (skip "-- Select --")
                    }

                    // Pre-fill subject with customer name and lead ID
                    subjectInput.value = `Follow-up for Lead #${leadId} - ${customerName}`;

                    // Pre-fill message with a professional template
                    messageBody.value = `Dear ${customerName},

Thank you for your interest in MJ Hauling United LLC's vehicle shipping services.

I wanted to follow up regarding your shipping quote request (Lead #${leadId}).

If you have any questions or would like to proceed with the booking, please don't hesitate to contact us.

Best regards,
MJ Hauling United LLC Team
Call or Text: +****************
Email: <EMAIL>`;

                    // Show modal
                    emailModal.style.display = 'flex';
                });
            });
        }

        // Function to close email modal
        function closeEmailModal() {
            const emailModal = document.getElementById('emailModal');
            emailModal.style.display = 'none';

            // Clear fields on close
            document.getElementById('to_email').value = '';
            document.getElementById('customer_name_hidden').value = '';
            document.getElementById('subject').value = '';
            document.getElementById('message_body').value = '';
            document.getElementById('from_email').selectedIndex = 0;
        }

        // Close modal when clicking outside of it
        window.addEventListener('click', function(event) {
            const emailModal = document.getElementById('emailModal');
            if (event.target === emailModal) {
                closeEmailModal();
            }
        });

        // Initialize email modal after DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeEmailModal();
            initializeDateRangeValidation();
        });

        // Function to validate date range and handle filter conflicts
        function initializeDateRangeValidation() {
            const exactDateInput = document.getElementById('filter_ship_date');
            const startDateInput = document.getElementById('filter_start_date');
            const endDateInput = document.getElementById('filter_end_date');
            const dateRangeGroup = document.querySelector('.date-range-group');

            function validateDateRange() {
                const startDate = startDateInput.value;
                const endDate = endDateInput.value;

                if (startDate && endDate && startDate > endDate) {
                    alert('Start date cannot be later than end date. Please adjust your date range.');
                    return false;
                }
                return true;
            }

            function updateFilterVisualState() {
                const exactDate = exactDateInput.value;

                if (exactDate) {
                    // Dim the date range inputs when exact date is used
                    dateRangeGroup.style.opacity = '0.5';
                    startDateInput.style.pointerEvents = 'none';
                    endDateInput.style.pointerEvents = 'none';
                } else {
                    // Restore normal state when exact date is cleared
                    dateRangeGroup.style.opacity = '1';
                    startDateInput.style.pointerEvents = 'auto';
                    endDateInput.style.pointerEvents = 'auto';
                }
            }

            // Add event listeners for real-time validation and visual feedback
            exactDateInput.addEventListener('change', updateFilterVisualState);
            startDateInput.addEventListener('change', validateDateRange);
            endDateInput.addEventListener('change', validateDateRange);

            // Initialize visual state on page load
            updateFilterVisualState();

            // Validate on form submission
            const searchForm = document.querySelector('.search-form');
            searchForm.addEventListener('submit', function(e) {
                if (!validateDateRange()) {
                    e.preventDefault();
                }
            });
        }
    </script>
</head>
<body>
    <div class="navbar">
        <div class="navbar-brand">
            <img class="navbar-logo" src="assets/img/logo/logo.png" alt="MJ Hauling United LLC">
        </div>

        <div class="navbar-container" id="navbarContainer">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="shippment_lead.php" class="nav-link">
                        <i class="fas fa-plus-circle"></i> New Lead
                    </a>
                </li>
            </ul>

            <div class="search-container">
                <!--<div class="filter-help-text">
                    <small>Filter by exact shipment date OR use date range. Exact date takes priority if both are selected.</small>
                </div> -->
                <form method="GET" action="view_leads.php" class="search-form">
                    <input type="text" id="search_query" name="search_query" placeholder="Search leads..." value="<?php echo htmlspecialchars($search_query); ?>">
                    <input type="date" id="filter_ship_date" name="filter_ship_date" title="Exact Shipment Date" value="<?php echo htmlspecialchars($filter_ship_date); ?>"> 
                    <div class="date-range-group">
                        <input type="date" id="filter_start_date" name="filter_start_date" title="Date Range: From" value="<?php echo htmlspecialchars($filter_start_date); ?>">
                        <span class="date-separator">to</span>
                        <input type="date" id="filter_end_date" name="filter_end_date" title="Date Range: To" value="<?php echo htmlspecialchars($filter_end_date); ?>">
                    </div>
                    <select id="filter_status" name="filter_status" title="Filter by Status">
                        <option value="All" <?php echo ($filter_status == 'All') ? 'selected' : ''; ?>>All Status</option>
                        <option value="Booked" <?php echo ($filter_status == 'Booked') ? 'selected' : ''; ?>>Booked</option>
                        <option value="Not Pick" <?php echo ($filter_status == 'Not Pick') ? 'selected' : ''; ?>>Not Pick</option>
                        <option value="Voice Mail" <?php echo ($filter_status == 'Voice Mail') ? 'selected' : ''; ?>>Voice Mail</option>
                        <option value="In Future Shipment" <?php echo ($filter_status == 'In Future Shipment') ? 'selected' : ''; ?>>In Future Shipment</option>
                        <option value="Quotation" <?php echo ($filter_status == 'Quotation') ? 'selected' : ''; ?>>Quotation</option>
                        <option value="Invalid Lead" <?php echo ($filter_status == 'Invalid Lead') ? 'selected' : ''; ?>>Invalid Lead</option>
                        <option value="Stop Lead" <?php echo ($filter_status == 'Stop Lead') ? 'selected' : ''; ?>>Stop Lead</option>
                        <option value="Already Booked"<?php echo ($filter_status == 'Already Booked') ? 'selected' : ''; ?>>Already Booked</option>
                        <option value="Delivered"<?php echo ($filter_status == 'Delivered') ? 'selected' : ''; ?>>Delivered</option>
                    </select>
                </form>

            </div>
            <button type="submit" title="Apply Filters"><i class="fas fa-search"></i> Search</button>
            <button type="button" onclick="resetFilters()" title="Clear All Filters"><i class="fas fa-undo"></i> Reset</button>


            <button class="logout-btn" onclick="window.location.href='?logout=true'" title="Logout">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
    </div>

    <div class="container">
        <div id="statusMessage" class="status-message <?php echo $status_type; ?>">
            <?php echo $status_message; ?>
        </div>

        <?php if (count($leads) > 0): ?>
            <div class="total-entries">Total Entries: <?php echo count($leads); ?></div>
            <div class="table-responsive-wrapper">
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Quote ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Quote Amount</th>
                            <th>Quote Date</th>
                            <th>Ship Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($leads as $lead): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($lead['id']); ?></td>
                                <td><?php echo htmlspecialchars($lead['quote_id']); ?></td>
                                <td><?php echo htmlspecialchars($lead['name']); ?></td>
                                <td><?php echo htmlspecialchars($lead['email']); ?></td>
                                <td><?php echo htmlspecialchars($lead['phone']); ?></td>
                                <td>$<?php echo htmlspecialchars(number_format($lead['quote_amount'], 2)); ?></td>
                                <td><?php echo htmlspecialchars($lead['formatted_quote_date']); ?></td>
                                <td><?php echo htmlspecialchars($lead['formatted_shippment_date']); ?></td>
                                <td><?php echo htmlspecialchars($lead['status']); ?></td>
                                <td class="actions">
                                    <a href="edit_lead.php?id=<?php echo htmlspecialchars($lead['id']); ?>" class="edit-btn">View Details & Edit</a>
                                    <?php if (!empty($lead['email'])): ?>
                                        <button class="email-btn"
                                                data-email-to="<?php echo htmlspecialchars($lead['email']); ?>"
                                                data-customer-name="<?php echo htmlspecialchars($lead['name']); ?>"
                                                data-lead-id="<?php echo htmlspecialchars($lead['id']); ?>"
                                                title="Send Email to <?php echo htmlspecialchars($lead['name']); ?>">
                                            <i class="fas fa-envelope"></i> Email
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="no-leads">No leads found matching your criteria.</p>
        <?php endif; ?>
    </div>

    <!-- Email Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEmailModal()">&times;</span>
            <h3>Send Email</h3>
            <form id="emailForm" method="POST" action="view_leads.php">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                <input type="hidden" name="customer_name" id="customer_name_hidden">

                <div class="form-group">
                    <label for="to_email">To:</label>
                    <input type="email" id="to_email" name="to_email" readonly required>
                </div>

                <div class="form-group">
                    <label for="from_email">From (Your Email):</label>
                    <select id="from_email" name="from_email" required>
                        <?php
                        // Determine the primary email for the logged-in user
                        $primary_email = '';
                        $user_name = '';

                        if ($is_admin_logged_in) {
                            $primary_email = $_SESSION['admin_email'] ?? $email_config['from_email'];
                            $user_name = $_SESSION['admin_name'] ?? 'Admin';
                        } elseif ($is_user_logged_in) {
                            $primary_email = $_SESSION['user_email'] ?? $email_config['from_email'];
                            $user_name = $_SESSION['user_name'] ?? 'User';
                        }
                        ?>

                        <?php if ($is_admin_logged_in): ?>
                            <!-- Admin's personal email as primary option -->
                            <?php if (isset($_SESSION['admin_email'])): ?>
                                <option value="<?php echo htmlspecialchars($_SESSION['admin_email']); ?>" selected>
                                    <?php echo htmlspecialchars($_SESSION['admin_email']); ?> (<?php echo htmlspecialchars($user_name); ?>)
                                </option>
                            <?php endif; ?>

                            <!-- Company email as secondary option -->
                            <?php if ($email_config['from_email'] !== ($_SESSION['admin_email'] ?? '')): ?>
                                <option value="<?php echo htmlspecialchars($email_config['from_email']); ?>">
                                    <?php echo htmlspecialchars($email_config['from_email']); ?> (Company)
                                </option>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php if ($is_user_logged_in): ?>
                            <!-- User's personal email as primary option -->
                            <?php if (isset($_SESSION['user_email'])): ?>
                                <option value="<?php echo htmlspecialchars($_SESSION['user_email']); ?>" selected>
                                    <?php echo htmlspecialchars($_SESSION['user_email']); ?> (<?php echo htmlspecialchars($user_name); ?>)
                                </option>
                            <?php endif; ?>

                            <!-- Company email as secondary option -->
                            <?php if ($email_config['from_email'] !== ($_SESSION['user_email'] ?? '')): ?>
                                <option value="<?php echo htmlspecialchars($email_config['from_email']); ?>">
                                    <?php echo htmlspecialchars($email_config['from_email']); ?> (Company)
                                </option>
                            <?php endif; ?>
                        <?php endif; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="subject">Subject:</label>
                    <input type="text" id="subject" name="subject" required placeholder="Enter email subject">
                </div>

                <div class="form-group">
                    <label for="message_body">Message:</label>
                    <textarea id="message_body" name="message_body" required placeholder="Type your message here..."></textarea>
                </div>

                <div class="modal-buttons">
                    <button type="button" class="close-btn" onclick="closeEmailModal()">Cancel</button>
                    <button type="submit" name="send_email" class="send-btn">
                        <i class="fas fa-paper-plane"></i> Send Email
                    </button>
                </div>
            </form>
        </div>
    </div>

    <footer>Powered by Desired Technologies</footer>
</body>
</html>